/*
 * <PERSON><PERSON><PERSON><PERSON> & Felipe's Wedding Flipbook
 * Interactive JavaScript functionality with music player and responsive navigation
 * Compatible with desktop and mobile browsers
 */

$(document).ready(function() {
    // Hide loading screen after everything is loaded
    $(window).on('load', function() {
        setTimeout(() => {
            $('#loadingScreen').addClass('hidden');
        }, 1000);
    });
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    // Set responsive dimensions
    let flipbookWidth = 800;
    let flipbookHeight = 600;

    if (window.innerWidth <= 480) {
        flipbookWidth = Math.min(window.innerWidth * 0.95, 400);
        flipbookHeight = flipbookWidth * 0.75;
    } else if (window.innerWidth <= 768) {
        flipbookWidth = Math.min(window.innerWidth * 0.9, 600);
        flipbookHeight = flipbookWidth * 0.75;
    }

    // Initialize flipbook with error handling
    try {
        $('#flipbook').turn({
            width: flipbookWidth,
            height: flipbookHeight,
            elevation: 0,        // Disable built-in elevation shadows
            gradients: false,    // Disable built-in gradients
            autoCenter: true,
            acceleration: !isMobile, // Disable acceleration on mobile for better performance
            display: isMobile ? 'single' : 'double' // Single page mode on mobile
        });
    } catch (error) {
        console.error('Flipbook initialization error:', error);
        // Fallback: show a simple message
        $('#flipbook').html('<div style="text-align: center; padding: 50px; color: white;">Please refresh the page to view the flipbook.</div>');
    }



    // Smooth transition to left position
    function smoothTransitionToLeft() {
        if (musicPlayer.classList.contains('top-position')) {
            musicPlayer.classList.add('fade-out');
            setTimeout(() => {
                musicPlayer.classList.remove('top-position');
                $('.centered-wrapper').removeClass('book-open');
                musicPlayer.classList.remove('fade-out');
            }, 400);
        }
    }

    // Smooth transition to top position
    function smoothTransitionToTop() {
        if (!musicPlayer.classList.contains('top-position')) {
            musicPlayer.classList.add('fade-out');
            setTimeout(() => {
                musicPlayer.classList.add('top-position');
                $('.centered-wrapper').addClass('book-open');
                musicPlayer.classList.remove('fade-out');
            }, 400);
        }
    }

    // Navigation Elements
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const currentPageSpan = document.getElementById('currentPage');

    const thumbnails = document.querySelectorAll('.thumbnail');
    let currentPage = 1;
    const totalPages = 8;

    // Update page indicator
    function updatePageIndicator(page) {
        currentPage = page;
        currentPageSpan.textContent = page;

        // Update navigation buttons
        prevBtn.disabled = (page === 1);
        nextBtn.disabled = (page === totalPages);

        // Update thumbnails
        thumbnails.forEach((thumb, index) => {
            thumb.classList.toggle('active', index + 1 === page);
        });
    }

    // Navigation button functionality
    prevBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            $('#flipbook').turn('previous');
        }
    });

    nextBtn.addEventListener('click', function() {
        if (currentPage < totalPages) {
            $('#flipbook').turn('next');
        }
    });

    // Thumbnail navigation
    thumbnails.forEach((thumbnail, index) => {
        thumbnail.addEventListener('click', function() {
            const targetPage = index + 1;
            $('#flipbook').turn('page', targetPage);
        });
    });

    // Update the page turning event handler
    $('#flipbook').bind('turning', function(_, page) {
        updatePageIndicator(page);

        var wrapper = $('.centered-wrapper');

        // When book is closed on front page, center it and keep music player on left
        if (page === 1) {
            wrapper.css('transform', 'translateX(0)');
            smoothTransitionToLeft();
        }
        // When book is closed on back page, move slightly to the right
        else if (page === 8) {
            wrapper.css('transform', 'translateX(150px)');
            smoothTransitionToLeft();
        }
        // When book is open (middle pages), shift it right and move music player to top
        else {
            wrapper.css('transform', 'translateX(70px)');
            smoothTransitionToTop();
        }
    });

    // Enhanced keyboard navigation
    $(window).bind('keydown', function(e){
        if (e.keyCode==37) { // Left arrow
            $('#flipbook').turn('previous');
        }
        else if (e.keyCode==39) { // Right arrow
            $('#flipbook').turn('next');
        }
        else if (e.keyCode==36) { // Home key - go to first page
            $('#flipbook').turn('page', 1);
        }
        else if (e.keyCode==35) { // End key - go to last page
            $('#flipbook').turn('page', totalPages);
        }
        else if (e.keyCode >= 49 && e.keyCode <= 56) { // Number keys 1-8
            const pageNum = e.keyCode - 48;
            $('#flipbook').turn('page', pageNum);
        }
    });

    // Initialize page indicator
    updatePageIndicator(1);

    // Auto-hide navigation help after 8 seconds (longer on mobile)
    const hideDelay = isMobile ? 12000 : 8000;
    setTimeout(() => {
        const navHelp = document.getElementById('navHelp');
        if (navHelp) {
            navHelp.style.opacity = '0';
            navHelp.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                navHelp.style.display = 'none';
            }, 500);
        }
    }, hideDelay);

    // Handle window resize for responsive design
    $(window).resize(function() {
        let newWidth = 800;
        let newHeight = 600;

        if (window.innerWidth <= 480) {
            newWidth = Math.min(window.innerWidth * 0.95, 400);
            newHeight = newWidth * 0.75;
        } else if (window.innerWidth <= 768) {
            newWidth = Math.min(window.innerWidth * 0.9, 600);
            newHeight = newWidth * 0.75;
        }

        $('#flipbook').turn('size', newWidth, newHeight);
    });



    // Music Player Functionality
    const audioPlayer = document.getElementById('audioPlayer');
    const playButton = document.getElementById('playButton');
    const visualizer = document.getElementById('visualizer');
    const bars = visualizer.querySelectorAll('.bar');
    const musicPlayer = document.querySelector('.music-player');
    const volumeSlider = document.getElementById('volumeSlider');
    let isPlaying = false;

    // Set initial volume
    audioPlayer.volume = 0.7;

    // Additional initialization after DOM is ready
    setTimeout(() => {
        if (audioPlayer.paused && !isPlaying) {
            audioPlayer.muted = false;
            audioPlayer.play().then(() => {
                isPlaying = true;
                playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>';
                startVisualizer();
            }).catch(error => {
                console.log('Auto-play delayed start prevented:', error);
            });
        }
    }, 500);

    // Initialize music player state and ensure audio plays
    audioPlayer.addEventListener('loadeddata', function() {
        // Unmute and try to play the audio
        audioPlayer.muted = false;
        audioPlayer.play().then(() => {
            isPlaying = true;
            playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>';
            startVisualizer();
        }).catch(error => {
            console.log('Auto-play prevented by browser:', error);
            // Set up for manual play
            isPlaying = false;
            playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>';
        });
    });

    // Fallback: Try to play after user interaction
    document.addEventListener('click', function() {
        if (!isPlaying && audioPlayer.paused) {
            audioPlayer.muted = false;
            audioPlayer.play().then(() => {
                isPlaying = true;
                playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>';
                startVisualizer();
            }).catch(error => {
                console.log('Could not play audio:', error);
            });
        }
    }, { once: true });

    // Handle play event
    audioPlayer.addEventListener('play', function() {
        isPlaying = true;
        playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>';
        startVisualizer();
    });

    // Handle pause event
    audioPlayer.addEventListener('pause', function() {
        isPlaying = false;
        playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>';
        stopVisualizer();
    });

    // Volume control functionality
    volumeSlider.addEventListener('input', function() {
        try {
            audioPlayer.volume = this.value / 100;
        } catch (error) {
            console.log('Volume control error:', error);
        }
    });

    // Play/Pause functionality
    playButton.addEventListener('click', function() {
        if (isPlaying) {
            audioPlayer.pause();
            isPlaying = false;
            playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M8 5v14l11-7z"/></svg>';
            stopVisualizer();
        } else {
            audioPlayer.play();
            isPlaying = true;
            playButton.innerHTML = '<svg width="24" height="24" viewBox="0 0 24 24" fill="white"><path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/></svg>';
            startVisualizer();
        }
    });



    // Visualizer functions
    function startVisualizer() {
        bars.forEach((bar, index) => {
            setTimeout(() => {
                bar.classList.add('active');
            }, index * 100);
        });
    }

    function stopVisualizer() {
        bars.forEach(bar => {
            bar.classList.remove('active');
        });
    }

    // Random visualizer animation while playing
    function animateVisualizer() {
        if (isPlaying && audioPlayer && !audioPlayer.paused) {
            bars.forEach(bar => {
                const height = Math.random() * 30 + 5;
                bar.style.height = height + 'px';
            });
        }
    }

    // Update visualizer every 200ms when playing
    const visualizerInterval = setInterval(animateVisualizer, 200);

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        if (visualizerInterval) {
            clearInterval(visualizerInterval);
        }
        if (audioPlayer && !audioPlayer.paused) {
            audioPlayer.pause();
        }
    });
});