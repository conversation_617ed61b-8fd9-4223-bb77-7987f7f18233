/*
 * <PERSON><PERSON><PERSON><PERSON> & Felipe's Wedding Flipbook
 * Interactive wedding album with music player and responsive design
 * Optimized for desktop and mobile browsers
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

html, body{
    height: 100%;
    margin: 0;
}

body{
	background-color: #f4f4f4;
    font-family: "Poppins", sans-serif;
    position: relative;
    overflow: hidden;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

/* Video background with overlay */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
    filter: blur(1px) brightness(0.8);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-content h2 {
    margin: 0 0 10px 0;
    font-size: 24px;
    font-weight: 600;
}

.loading-content p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Add a subtle overlay for better contrast */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.1) 100%);
    z-index: -1;
    pointer-events: none;
}

.centered-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    transition: transform 0.6s ease-in-out;
    gap: 60px;
}

.centered-wrapper.book-open {
    flex-direction: column;
    gap: 30px;
}

/* Music Player Styles */
.music-player {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    width: 280px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: scale(1);
}

/* Fade out animation */
.music-player.fade-out {
    opacity: 0;
    transform: scale(0.9);
}

/* Music player when moved to top */
.music-player.top-position {
    flex-direction: row;
    width: auto;
    padding: 15px 25px;
    border-radius: 50px;
    gap: 20px;
    opacity: 1;
    transform: scale(1);
}

.music-player.top-position .album-cover {
    width: 60px;
    height: 60px;
    margin-bottom: 0;
    border-radius: 50%;
}

.music-player.top-position .play-button {
    width: 30px;
    height: 30px;
}

.music-player.top-position .play-button svg {
    width: 16px;
    height: 16px;
}

.music-player.top-position .song-info {
    margin-bottom: 0;
    text-align: left;
}

.music-player.top-position .song-info h3 {
    font-size: 16px;
    margin-bottom: 2px;
}

.music-player.top-position .song-info p {
    font-size: 12px;
}

.music-player.top-position .audio-visualizer {
    margin-top: 0;
    height: 30px;
}

.music-player.top-position .volume-control {
    margin-bottom: 0;
}

/* Volume Control Styles */
.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    width: 100%;
    justify-content: center;
}

.volume-slider {
    width: 120px;
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.3);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
}

.volume-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Volume control in top position */
.music-player.top-position .volume-control {
    width: auto;
}

.music-player.top-position .volume-slider {
    width: 80px;
}

/* Navigation Controls */
.navigation-controls {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    border-radius: 50px;
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: scale(1);
}

.page-indicator {
    color: white;
    font-weight: 600;
    font-size: 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    min-width: 60px;
    text-align: center;
}

/* Navigation Help */
.navigation-help {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    color: white;
    padding: 20px;
    border-radius: 15px;
    z-index: 1001;
    max-width: 300px;
    animation: slideInLeft 0.5s ease;
}

.help-content h4 {
    margin: 0 0 10px 0;
    color: #4ecdc4;
    font-size: 16px;
}

.help-content p {
    margin: 5px 0;
    font-size: 12px;
    opacity: 0.9;
}

.close-help {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.close-help:hover {
    opacity: 1;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Page Thumbnails */
.page-thumbnails {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 10px;
    z-index: 1000;
    max-height: 80vh;
    overflow-y: auto;
    padding: 10px;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border-radius: 15px;
}

.thumbnail {
    position: relative;
    width: 60px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    opacity: 0.7;
}

.thumbnail:hover {
    transform: scale(1.1);
    opacity: 1;
}

.thumbnail.active {
    border-color: #4ecdc4;
    opacity: 1;
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.5);
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.thumbnail span {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .centered-wrapper {
        padding: 10px;
        gap: 20px;
    }

    .centered-wrapper.book-open {
        gap: 15px;
    }

    #flipbook {
        width: 90vw;
        height: calc(90vw * 0.75);
        max-width: 600px;
        max-height: 450px;
    }

    .page-thumbnails {
        right: 5px;
        width: 45px;
        padding: 5px;
        gap: 5px;
    }

    .thumbnail {
        width: 35px;
        height: 50px;
    }

    .thumbnail span {
        font-size: 8px;
        padding: 1px 3px;
    }

    .navigation-controls {
        bottom: 10px;
        padding: 8px 15px;
        gap: 10px;
    }

    .nav-btn {
        width: 35px;
        height: 35px;
    }

    .nav-btn svg {
        width: 18px;
        height: 18px;
    }

    .page-indicator {
        font-size: 12px;
        min-width: 40px;
    }

    .music-player {
        width: 200px;
        padding: 15px;
    }

    .album-cover {
        width: 120px;
        height: 120px;
    }

    .song-info h3 {
        font-size: 18px;
    }

    .song-info p {
        font-size: 12px;
    }

    .volume-slider {
        width: 100px;
    }

    .music-player.top-position {
        padding: 10px 15px;
        gap: 15px;
    }

    .music-player.top-position .album-cover {
        width: 45px;
        height: 45px;
    }

    .music-player.top-position .song-info h3 {
        font-size: 14px;
    }

    .music-player.top-position .song-info p {
        font-size: 10px;
    }

    .music-player.top-position .volume-slider {
        width: 60px;
    }

    .navigation-help {
        top: 10px;
        left: 10px;
        padding: 15px;
        max-width: 250px;
    }

    .help-content h4 {
        font-size: 14px;
    }

    .help-content p {
        font-size: 11px;
    }
}

/* Extra small phones */
@media (max-width: 480px) {
    #flipbook {
        width: 95vw;
        height: calc(95vw * 0.75);
    }

    .music-player {
        width: 180px;
        padding: 12px;
    }

    .album-cover {
        width: 100px;
        height: 100px;
    }

    .song-info h3 {
        font-size: 16px;
    }

    .volume-slider {
        width: 80px;
    }

    .music-player.top-position {
        padding: 8px 12px;
        gap: 10px;
    }

    .music-player.top-position .album-cover {
        width: 35px;
        height: 35px;
    }

    .music-player.top-position .song-info h3 {
        font-size: 12px;
    }

    .music-player.top-position .song-info p {
        font-size: 9px;
    }

    .music-player.top-position .volume-slider {
        width: 50px;
    }

    .page-thumbnails {
        right: 2px;
        width: 40px;
    }

    .thumbnail {
        width: 30px;
        height: 40px;
    }

    .navigation-controls {
        bottom: 5px;
        padding: 6px 12px;
        gap: 8px;
    }

    .nav-btn {
        width: 30px;
        height: 30px;
    }

    .nav-btn svg {
        width: 16px;
        height: 16px;
    }

    .navigation-help {
        top: 5px;
        left: 5px;
        padding: 10px;
        max-width: 200px;
    }
}

/* Touch-friendly hover effects for mobile */
@media (hover: none) {
    .thumbnail:hover {
        transform: none;
    }

    .nav-btn:hover {
        transform: none;
    }

    .album-cover:hover {
        transform: none;
    }

    .thumbnail:active {
        transform: scale(1.1);
    }

    .nav-btn:active {
        transform: scale(1.1);
    }

    .album-cover:active {
        transform: scale(1.05);
    }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .centered-wrapper {
        gap: 15px;
    }

    .centered-wrapper.book-open {
        gap: 10px;
    }

    #flipbook {
        width: 80vw;
        height: calc(80vw * 0.6);
        max-height: 70vh;
    }

    .music-player {
        width: 160px;
        padding: 10px;
    }

    .album-cover {
        width: 80px;
        height: 80px;
    }

    .music-player.top-position .album-cover {
        width: 30px;
        height: 30px;
    }
}

.album-cover {
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 20px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.album-cover:hover {
    transform: scale(1.05);
}

.album-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.play-button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 1;
}

.song-info {
    text-align: center;
    margin-bottom: 20px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.song-info h3 {
    margin: 0 0 5px 0;
    font-size: 24px;
    font-weight: 600;
}

.song-info p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
}

.audio-visualizer {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 40px;
    margin-top: 10px;
}

.bar {
    width: 6px;
    background: linear-gradient(to top, #ff6b6b, #4ecdc4);
    border-radius: 3px;
    transition: height 0.1s ease;
    height: 5px;
}

.bar.active {
    animation: bounce 0.6s ease-in-out infinite alternate;
}

@keyframes bounce {
    0% { height: 5px; }
    100% { height: 35px; }
}

#flipbook{
	width:800px;
	height:600px;
    position: relative;
    border-radius: 8px;
}



#flipbook .page{
	background-color:white;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
    overflow: hidden;
}

#flipbook .page img{
    width: 100%;
    height: 100%;
    border-radius: 6px;
}

#flipbook .hard{
	background-color:#c0392b !important;
    color: #fff;
    font-weight: bold;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2em;
}

/* Remove any turn.js generated shadows */
#flipbook .shadow,
#flipbook .gradient {
    display: none !important;
}

#flipbook .page.p2,
#flipbook .page.p3{
	left:0;
	top:0;
}

#flipbook .page.p1{
	left:0;
	top:0;
    background-image: url('../assets/images/1.png');
    background-position: center;
    background-size: cover;
}

#flipbook .page.p1 .side.odd{
	background-position:-100% 0;
}

#flipbook .page.p1 .side.even{
	background-position:0 0;
}

#flipbook .page.last-page{
	left:0;
	top:0;
}

#flipbook .page.last-page .side.odd{
	background-position:0 0;
}

#flipbook .page.last-page .side.even{
	background-position:-100% 0;
}

#flipbook .turn-page{
	background-color:transparent;
}



/* Responsive adjustments */
@media (max-width: 900px) {
    #flipbook {
        width: 90vw;
        height: calc(90vw * 0.75);
        max-width: 600px;
        max-height: 450px;
    }
    
    .centered-wrapper {
        padding: 20px;
        box-sizing: border-box;
    }
}
