<!DOCTYPE html>
<!--
    <PERSON><PERSON><PERSON><PERSON> & <PERSON>'s Wedding Flipbook
    Interactive wedding album with music player and responsive design
    Optimized for desktop and mobile browsers
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <title><PERSON><PERSON><PERSON><PERSON> & <PERSON>'s Wedding</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="http://www.turnjs.com/lib/turn.min.js"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>Loading Wedding Invites...</h2>
            <p>FELIPE & DHAENNA</p>
        </div>
    </div>

    <!-- Video Background -->
    <video class="video-background" autoplay muted loop>
        <source src="assets/bg.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <div class="centered-wrapper">
        <!-- Music Player -->
        <div class="music-player">
            <div class="album-cover">
                <img src="assets/music/song_bg.jpg" alt="Album Cover">
                <div class="play-button" id="playButton">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
            </div>
            <div class="song-info">
                <h3>Only</h3>
                <p>Lee Hi(이하이) | covered by JASMINE</p>
            </div>
            <div class="volume-control">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                </svg>
                <input type="range" id="volumeSlider" min="0" max="100" value="70" class="volume-slider">
            </div>
            <div class="audio-visualizer" id="visualizer">
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
                <div class="bar"></div>
            </div>
            <audio id="audioPlayer" preload="auto" autoplay loop muted>
                <source src="assets/music/song.mp3" type="audio/mpeg">
                Your browser does not support the audio element.
            </audio>
        </div>

        <div id="flipbook">
            <div class="hard"><img src="assets/images/1.png" alt="Page 1"></div>
            <div><img src="assets/images/2.png" alt="Page 2"></div>
            <div><img src="assets/images/3.png" alt="Page 3"></div>
            <div><img src="assets/images/4.png" alt="Page 4"></div>
            <div><img src="assets/images/5.png" alt="Page 5"></div>
            <div><img src="assets/images/6.png" alt="Page 6"></div>
            <div><img src="assets/images/7.png" alt="Page 7"></div>
            <div class="hard"><img src="assets/images/8.png" alt="Page 8"></div>
        </div>

        <!-- Navigation Controls -->
        <div class="navigation-controls">
            <button class="nav-btn prev-btn" id="prevBtn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                    <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                </svg>
            </button>
            <div class="page-indicator">
                <span id="currentPage">1</span> / <span id="totalPages">8</span>
            </div>
            <button class="nav-btn next-btn" id="nextBtn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                </svg>
            </button>
        </div>

        <!-- Navigation Instructions -->
        <div class="navigation-help" id="navHelp">
            <div class="help-content">
                <h4>Navigation Tips</h4>
                <p>• Use arrow keys or navigation buttons</p>
                <p>• Click thumbnails to jump to any page</p>
                <p>• Press number keys 1-8 for quick access</p>
                <p>• Drag page corners to turn pages</p>
            </div>
            <button class="close-help" onclick="document.getElementById('navHelp').style.display='none'">×</button>
        </div>

        <!-- Page Thumbnails -->
        <div class="page-thumbnails" id="thumbnails">
            <div class="thumbnail active" data-page="1">
                <img src="assets/images/1.png" alt="Page 1">
                <span>1</span>
            </div>
            <div class="thumbnail" data-page="2">
                <img src="assets/images/2.png" alt="Page 2">
                <span>2</span>
            </div>
            <div class="thumbnail" data-page="3">
                <img src="assets/images/3.png" alt="Page 3">
                <span>3</span>
            </div>
            <div class="thumbnail" data-page="4">
                <img src="assets/images/4.png" alt="Page 4">
                <span>4</span>
            </div>
            <div class="thumbnail" data-page="5">
                <img src="assets/images/5.png" alt="Page 5">
                <span>5</span>
            </div>
            <div class="thumbnail" data-page="6">
                <img src="assets/images/6.png" alt="Page 6">
                <span>6</span>
            </div>
            <div class="thumbnail" data-page="7">
                <img src="assets/images/7.png" alt="Page 7">
                <span>7</span>
            </div>
            <div class="thumbnail" data-page="8">
                <img src="assets/images/8.png" alt="Page 8">
                <span>8</span>
            </div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>